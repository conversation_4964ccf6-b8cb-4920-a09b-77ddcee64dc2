function openModal() {
    $('#contactModal').modal('show');
}

// Enhanced Lazy Loading Implementation
document.addEventListener("DOMContentLoaded", function () {
    // Lazy loading for carousel background images
    let lazyCarouselItems = document.querySelectorAll(".carousel-item[data-bg]");

    // Lazy loading for regular images with data-src
    let lazyImages = document.querySelectorAll("img[data-src]");

    // Intersection Observer for carousel background images
    let carouselObserver = new IntersectionObserver((entries) => {
        entries.forEach((entry) => {
            if (entry.isIntersecting) {
                let item = entry.target;
                let bgImage = item.getAttribute("data-bg");
                if (bgImage) {
                    // Preload the image before setting as background
                    let img = new Image();
                    img.onload = function() {
                        item.style.backgroundImage = `url('${bgImage}')`;
                        item.classList.add("loaded");
                    };
                    img.onerror = function() {
                        console.warn('Failed to load carousel background image:', bgImage);
                        item.classList.add("error");
                    };
                    img.src = bgImage;
                }
                carouselObserver.unobserve(item);
            }
        });
    }, {
        rootMargin: "100px",
        threshold: 0.1
    });

    // Intersection Observer for regular images
    let imageObserver = new IntersectionObserver((entries) => {
        entries.forEach((entry) => {
            if (entry.isIntersecting) {
                let img = entry.target;
                let src = img.getAttribute("data-src");
                if (src) {
                    // Add loading class for visual feedback
                    img.classList.add("loading");

                    img.onload = function() {
                        img.classList.remove("loading");
                        img.classList.add("loaded");
                    };

                    img.onerror = function() {
                        img.classList.remove("loading");
                        img.classList.add("error");
                        console.warn('Failed to load image:', src);
                        // Set a fallback image if available
                        if (img.getAttribute("data-fallback")) {
                            img.src = img.getAttribute("data-fallback");
                        }
                    };

                    img.src = src;
                    img.removeAttribute("data-src");
                }
                imageObserver.unobserve(img);
            }
        });
    }, {
        rootMargin: "50px",
        threshold: 0.1
    });

    // Observe all lazy elements
    lazyCarouselItems.forEach((item) => carouselObserver.observe(item));
    lazyImages.forEach((img) => imageObserver.observe(img));

    // Fallback for browsers that don't support Intersection Observer
    if (!('IntersectionObserver' in window)) {
        // Load all images immediately for older browsers
        lazyImages.forEach((img) => {
            let src = img.getAttribute("data-src");
            if (src) {
                img.src = src;
                img.removeAttribute("data-src");
            }
        });

        lazyCarouselItems.forEach((item) => {
            let bgImage = item.getAttribute("data-bg");
            if (bgImage) {
                item.style.backgroundImage = `url('${bgImage}')`;
            }
        });
    }
});

$(document).ready(function () {
    // Select the carousel element
    $('#carouselExampleCaptions').carousel({
        interval: 5000, // Enable auto-sliding with 5 second interval
        ride: 'carousel' // Enable automatic start
    });
});


$(document).ready(function() {
    // Handle video thumbnail clicks
    $('.video-link').click(function(e) {
        e.preventDefault();
        var videoId = $(this).data('video');
        var iframe = $('#videoModal iframe');
        iframe.attr('src', 'https://www.youtube.com/embed/' + videoId + '?autoplay=1');
        $('#videoModal').modal('show');
    });

    // Clear iframe src when modal is closed
    $('#videoModal').on('hidden.bs.modal', function() {
        $('#videoModal iframe').attr('src', '');
    });
});