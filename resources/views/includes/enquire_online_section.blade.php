<div class="counter-top-area counter-top-area-1" id="check-appointment">
    <div class="form-overlay"></div>
    <div class="container">
        <div class="row rs-vertical-middle">
            <div class="col-md-6 col-sm-7 support-section">
                <div class="form-inner inner-part">
                    <form id="quick-form" method="POST" action="{{route('submit.enquiry')}}" name="enquireOnlineForm">
                        @csrf
                        <div class="need-query">
                            <h3>Enquire Online Now</h3>
                            @if(session('success'))
                                <p class="text-center" style="color:green">
                                    {{ session('success') }}
                                </p>
                            @endif
                            @if(session('error'))
                                <p class="text-center" style="color:red">
                                    {{ session('error') }}
                                </p>
                            @endif
                            @if($errors->any())
                                <p class="text-center" style="color:red">
                                    @foreach($errors->all() as $error)
                                        {{ $error }}<br>
                                    @endforeach
                                </p>
                            @endif
                        </div>
                        <div class="quick-form">
                            <input type="hidden" name="url" value="{{url()->current()}}">
                            <input type="hidden" name="utm_source" id="utm_source" value="">
                            <input type="hidden" name="utm_medium" id="utm_medium" value="">
                            <input type="hidden" name="utm_campaign" id="utm_campaign" value="">
                            <input type="hidden" name="utm_term" id="utm_term" value="">

                            <input type="text" name="clientname" id="clientname" placeholder="Name" autocomplete="off" required>
                            <input type="text" name="phone" id="cphone" placeholder="Phone Number" autocomplete="off" required>
                            <input type="email" name="email" id="cemail" placeholder="Email Id" autocomplete="off">
                            <div class="form-group">
                                <select id="propertylocation" name="propertylocation" class="form-select" required>
                                    <option value="" disabled selected>Select City</option>
                                    @forelse ($cities as $city)
                                        <option value="{{$city->name}}">{{$city->name}}</option>
                                    @empty

                                    @endforelse
                                    <option value="Others">Others</option>
                                </select>
                            </div>

                            <div class="form-group mb-3" id="othersCityGroup" style="display: none;">
                                <input type="text" id="othersCity" name="othersCity"  placeholder="Enter your city" value="" >
                            </div>
                            <div class="form-group">
                                <select class="form-select" id="project-needs" name="project-needs" required>
                                    <option value="" disabled selected>Select your project need</option>
                                    <option value="full-house-interiors">Full House Interiors</option>
                                    <option value="kitchen-wardrobe">Kitchen, Wardrobe</option>
                                    <option value="only-decor-items">Only Decor Items</option>
                                    <option value="only-civil-work">Only Civil Work</option>
                                </select>
                            </div>
                            {{-- <input type="checkbox" name="check"><label>I authorize {{config('app.name')}} to contact me.</label> <br> --}}
                            <input class="submit" id="btnSubmit" type="submit" value=" Get Free Estimate">
                        </div>
                    </form>

                    <script>
                        document.addEventListener('DOMContentLoaded', function() {
                            // Show "Others" city input field when "Others" is selected
                            const propertyLocation = document.getElementById('propertylocation');
                            const othersCityGroup = document.getElementById('othersCityGroup');

                            if (propertyLocation) {
                                propertyLocation.addEventListener('change', function() {
                                    if (this.value === 'Others') {
                                        othersCityGroup.style.display = 'block';
                                    } else {
                                        othersCityGroup.style.display = 'none';
                                    }
                                });
                            }
                        });
                    </script>
                </div>
            </div>
            <div class="col-md-6 col-sm-5 col-xs-12">
                <div class="rs-count sidebar-count mpt-40">
                    <div class="row">
                        <div class="col-sm-6  wow fadeInUp" data-wow-duration=".3s" data-wow-delay="300ms">
                            <div class="rs-counter-list list">
                                <h3>{{config('app.projects_delivered')}}+</h3>
                                <h4> Projects</h4>
                            </div>
                        </div>
                        <div class="col-sm-6  wow fadeInUp" data-wow-duration=".7s" data-wow-delay="300ms">
                            <div class="rs-counter-list list">
                                <h3>{{config('app.team_size')}}+ </h3>
                                <h4>Expert Professionals</h4>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-12 text-center  wow fadeInUp" data-wow-duration=".9s" data-wow-delay="300ms">
                            <div class="rs-counter-list ">
                                <h3>10+</h3>
                                <h4>Years Of Expertise</h4>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>