<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Lazy Loading Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 40px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-image {
            max-width: 100%;
            height: auto;
            margin: 10px 0;
            border: 2px solid #ddd;
        }
        .carousel-test {
            height: 300px;
            background-color: #eee;
            margin: 10px 0;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            font-size: 18px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .spacer {
            height: 100vh;
            background: linear-gradient(45deg, #f0f0f0, #e0e0e0);
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            font-size: 24px;
        }
        
        /* Include the lazy loading styles */
        img[data-src] {
            opacity: 0;
            transition: opacity 0.3s ease-in-out;
        }
        
        img.loading {
            opacity: 0.5;
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            animation: loading 1.5s infinite;
        }
        
        img.loaded {
            opacity: 1;
        }
        
        img.error {
            opacity: 0.7;
            filter: grayscale(100%);
        }
        
        @keyframes loading {
            0% { background-position: 200% 0; }
            100% { background-position: -200% 0; }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Lazy Loading Implementation Test</h1>
        
        <div class="status info">
            <strong>Instructions:</strong> Open browser developer tools (F12) → Network tab → Reload page. 
            Only images above the fold should load initially. Scroll down to see lazy loading in action.
        </div>
        
        <div class="test-section">
            <h2>Test 1: Above the Fold Images (Should Load Immediately)</h2>
            <img src="data:image/png;base64,R0lGODlhAQABAAD/ACwAAAAAAQABAAACADs=" 
                 data-src="images/new_logo3.png" 
                 alt="Logo Test" 
                 class="test-image"
                 loading="lazy">
            <div class="status success">✓ This image should load immediately as it's above the fold</div>
        </div>
        
        <div class="spacer">
            Scroll down to test lazy loading...
        </div>
        
        <div class="test-section">
            <h2>Test 2: Below the Fold Images (Should Load When Scrolled Into View)</h2>
            <img src="data:image/png;base64,R0lGODlhAQABAAD/ACwAAAAAAQABAAACADs=" 
                 data-src="images/A1.jpg" 
                 alt="Service Image Test" 
                 class="test-image"
                 loading="lazy"
                 data-fallback="images/new_logo3.png">
            <div class="status info">⏳ This image should only load when you scroll to this section</div>
        </div>
        
        <div class="spacer">
            Keep scrolling...
        </div>
        
        <div class="test-section">
            <h2>Test 3: Portfolio Images</h2>
            <img src="data:image/png;base64,R0lGODlhAQABAAD/ACwAAAAAAQABAAACADs=" 
                 data-src="images/portfolios/MBR.jpg" 
                 alt="Portfolio Test" 
                 class="test-image"
                 loading="lazy"
                 data-fallback="images/placeholder-portfolio.svg">
            <div class="status info">⏳ Portfolio image with fallback placeholder</div>
        </div>
        
        <div class="spacer">
            More content...
        </div>
        
        <div class="test-section">
            <h2>Test 4: Error Handling (Broken Image)</h2>
            <img src="data:image/png;base64,R0lGODlhAQABAAD/ACwAAAAAAQABAAACADs=" 
                 data-src="images/non-existent-image.jpg" 
                 alt="Error Test" 
                 class="test-image"
                 loading="lazy"
                 data-fallback="images/A1.jpg">
            <div class="status error">⚠ This should show fallback image when original fails to load</div>
        </div>
        
        <div class="test-section">
            <h2>Test Results</h2>
            <div id="test-results">
                <div class="status info">Open browser console to see lazy loading logs</div>
            </div>
        </div>
    </div>

    <!-- Include the lazy loading script -->
    <script src="js/custom.js"></script>
    
    <script>
        // Additional test logging
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 Lazy Loading Test Started');
            
            // Monitor image loading
            const images = document.querySelectorAll('img[data-src]');
            console.log(`📊 Found ${images.length} images with lazy loading`);
            
            images.forEach((img, index) => {
                img.addEventListener('load', function() {
                    console.log(`✅ Image ${index + 1} loaded: ${this.src}`);
                });
                
                img.addEventListener('error', function() {
                    console.log(`❌ Image ${index + 1} failed: ${this.getAttribute('data-src')}`);
                });
            });
        });
    </script>
</body>
</html>
