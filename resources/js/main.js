// Main JavaScript file for asset optimization
// This file imports all the JS files currently used in masterlayout.blade.php

// Import local JavaScript files
import '../../public/js/owl.carousel.min.js';
import '../../public/js/main1.js';
import '../../public/js/custom.js';

// Note: External CDN JavaScript files will remain in the HTML for better caching
// These include:
// - jQuery 3.6.0
// - Bootstrap 4.5.2
// - Fancybox 3.5.2
// - Popper.js 2.11.6

// Inline JavaScript functions that were in the layout
window.Hide = function(HideID) { 
    HideID.style.display = "none"; 
};

// Document ready function for Fancybox initialization
document.addEventListener('DOMContentLoaded', function() {
    // Check if jQuery and Fancybox are loaded (from CDN)
    if (typeof $ !== 'undefined' && typeof $.fancybox !== 'undefined') {
        $(".fancybox").fancybox({
            openEffect: 'none',
            closeEffect: 'none'
        });
    }
});
